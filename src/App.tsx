import { useCallback, useEffect, useRef, useState } from 'react'
import './App.css'
import { HtmlNode } from './lib/types'
import StructurePanel from './builderComponents/StructurePanel'
import PropertiesPanel from './builderComponents/PropertiesPanel'
import { Button } from '@/components/ui/button.tsx'
// Import extracted builder modules
import {
    createElementClickHandler,
    handleKeyDown,
    makeElementDraggable,
    removeEventHandlers
} from './lib/builder/elementHandlers'
import { createTextEditHandler, isTextEditable } from './lib/builder/textEdit'
import { parseHtmlContent, updateIframeContent, updateIframeHeight } from './lib/builder/htmlParser'
import {
    drawHoverOverlay,
    drawOverlay,
    getElementAtPosition,
    setupHoverOverlayEventListeners,
    setupOverlayEventListeners
} from './lib/builder/canvasOverlay'
import { handleDragLeave, handleDragOver, handleDrop } from './lib/builder/dragDrop'
import { clearSavedFile, loadSavedFileData, openProject, saveHtmlContent } from './lib/builder/fileSystem'

// Utility function to create a debounced function
const debounce = <T extends (...args: any[]) => any>(func: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout
    return ((...args: any[]) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => func(...args), delay)
    }) as T
}

function App() {
    const [htmlContent, setHtmlContent] = useState<string>('')
    const [htmlStructure, setHtmlStructure] = useState<HtmlNode | null>(null)
    const [selectedElement, setSelectedElement] = useState<HTMLElement>()
    const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null)
    const [filename, setFilename] = useState<string>('')
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const fileHandleRef = useRef<FileSystemFileHandle | null>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const hoverCanvasRef = useRef<HTMLCanvasElement>(null)
    const dropIndicatorRef = useRef<HTMLDivElement>(null)
    const dragOverElemRef = useRef<HTMLElement | null>(null)
    const dragPosition = useRef<'above' | 'below' | 'inside'>('below')

    // Store event listener references for proper cleanup
    const eventListenersRef = useRef<Map<HTMLElement, {
        click: (e: MouseEvent) => void;
        dblclick?: (e: MouseEvent) => void;
        dragstart?: (e: DragEvent) => void;
        // mouseenter: (e: MouseEvent) => void;
        // mouseleave: (e: MouseEvent) => void;
    }>>(new Map())

    // Create element click handler using the extracted function
    const handleElementClick = useCallback((element: HTMLElement) => {
        return createElementClickHandler(element, (selectedElem) => {
            // When an element is selected, make it draggable
            const dragStartHandler = makeElementDraggable(selectedElem)

            // Store the dragstart handler for cleanup
            const existingHandlers = eventListenersRef.current.get(selectedElem) || {}
            // eslint-disable-next-line
            // @ts-ignore
            eventListenersRef.current.set(selectedElem, {
                ...existingHandlers,
                dragstart: dragStartHandler
            })

            // Add the dragstart event listener
            selectedElem.addEventListener('dragstart', dragStartHandler as EventListener)

            // Update the selected element state
            setSelectedElement(selectedElem)
        })
    }, [])

    // We're now using mousemove for hover detection instead of mouseenter/mouseleave

    // Function to draw the hover overlay using the extracted function
    const handleDrawHoverOverlay = useCallback((element: HTMLElement | null) => {
        if (hoverCanvasRef.current && iframeRef.current) {
            drawHoverOverlay(element, hoverCanvasRef.current, iframeRef.current)
        }
    }, [hoverCanvasRef, iframeRef])

    // Function to draw the overlay using the extracted function
    const handleDrawOverlay = useCallback((element: HTMLElement) => {
        if (canvasRef.current && iframeRef.current) {
            drawOverlay(element, canvasRef.current, iframeRef.current)
        }
    }, [canvasRef, iframeRef])
    // We're now using the imported isFileSystemAccessSupported function

    // Functions to handle drag and drop of components using the extracted functions
    const handleDragOverWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragOver(e, iframeRef.current, dragOverElemRef, dragPosition, dropIndicatorRef)
        }
    }, [iframeRef, dropIndicatorRef])


    // Function to save the current state of the iframe content back to the file using the extracted function
    const handleStyleUpdate = useCallback(async () => {
        // First, immediately redraw the canvas overlay when styles change

        // Then save the content to the file
        if (!iframeRef.current || !fileHandleRef.current) {
            console.warn('Cannot save: iframe or file handle not available.')
            return
        }

        saveHtmlContent(
            iframeRef.current,
            fileHandleRef.current,
            () => console.log('File saved successfully!'),
            (error) => console.error('Error saving file:', error),
            selectedElement,
            handleDrawOverlay
        )
    }, [handleDrawOverlay, selectedElement])

    // Create debounced save function with 500ms delay
    // const debouncedSaveTemplate = useCallback(
    //     debounce(handleStyleUpdate, 500),
    //     [handleStyleUpdate]
    // )


    const doubleClickHandler = useCallback((element: HTMLElement) => (event: MouseEvent) => {
            event.stopPropagation()
            event.preventDefault()

            const textEditHandler = createTextEditHandler(
                element,
                setHtmlContent,
                setSelectedElement,
                iframeRef.current as HTMLIFrameElement,
                () => {
                    // Redraw overlay immediately for visual feedback
                    // handleDrawOverlay(element)
                    // Save template with debounce
                    // debouncedSaveTemplate()
                    handleStyleUpdate()
                }
            )
            textEditHandler(event)
        // } , [handleDrawOverlay, debouncedSaveTemplate])
        } , [handleDrawOverlay])

    // Reusable function to setup event handlers for elements
    const setupElementEventHandlers = useCallback((element: HTMLElement) => {
        const clickHandler = handleElementClick(element)

        // Create text edit handler for double-click if element is text-editable
        let dblclickHandler
        if (isTextEditable(element)) {
            dblclickHandler = doubleClickHandler(element)
        }

        // Store event handlers for later cleanup
        eventListenersRef.current.set(element, {
            click: clickHandler,
            dblclick: dblclickHandler,
        })

        // Attach event listeners using the stored handlers
        element.addEventListener('click', clickHandler)
        if (dblclickHandler) {
            element.addEventListener('dblclick', dblclickHandler)
        }
    }, [handleElementClick, doubleClickHandler])

    const handleDropWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDrop(
                e,
                iframeRef.current,
                dragOverElemRef,
                dragPosition,
                (content) => {
                    setHtmlContent(content)
                    handleStyleUpdate()
                },
                setupElementEventHandlers,
                dropIndicatorRef
            )
        }
    }, [setupElementEventHandlers])

    const handleDragLeaveWrapper = useCallback((e: DragEvent) => {
        if (iframeRef.current && dropIndicatorRef.current) {
            handleDragLeave(e, iframeRef.current, dropIndicatorRef)
        }
    }, [iframeRef, dropIndicatorRef])

    // Load saved file handle on initial mount using the extracted function
    useEffect(() => {
        loadSavedFileData(
            (handle, content) => {
                if (handle) {
                    fileHandleRef.current = handle
                    if (content) {
                        setHtmlContent(content)
                    }
                }
            },
            (error) => {
                console.error('Error loading saved file:', error)
                fileHandleRef.current = null
            }
        )
    }, [])

    // Open project using the extracted function
    const handleOpenProject = async () => {
        openProject(
            (filename, content, handle) => {
                setFilename(filename)
                fileHandleRef.current = handle
                setHtmlContent(content)
            },
            (error) => {
                console.log('File picking was canceled or failed:', error)
            }
        )
    }

    // Clear saved file using the extracted function
    const handleClearSavedFile = async () => {
        clearSavedFile(() => {
            setHtmlContent('')
            setFilename('')
            setHtmlStructure(null)
            setSelectedElement(undefined)
            fileHandleRef.current = null
        })
    }

    // Parse HTML structure into a tree using the extracted function
    useEffect(() => {
        const htmlNode = parseHtmlContent(htmlContent)
        setHtmlStructure(htmlNode)
    }, [htmlContent])

    // Update iframe content when htmlContent changes and set up drag-and-drop using extracted functions
    useEffect(() => {
        if (!htmlContent || !iframeRef.current) return

        // Get iframe document
        const iframe = iframeRef.current
        const iframeWindow = iframe.contentWindow

        // Clear all existing event listeners from previous renders
        removeEventHandlers(eventListenersRef.current)

        // Update iframe content
        const iframeDocument = updateIframeContent(iframe, htmlContent)
        if (!iframeDocument) return

        const handleIframeMouseMove = (e: MouseEvent) => {
            // Get the element at the current mouse position
            const element = getElementAtPosition(e.clientX, e.clientY, iframe)

            // console.log('eee ->', element, hoveredElement)
            // Update the hovered element if it changed
            if (element !== hoveredElement) {
                setHoveredElement(element)
            }
        }

        // Add event handlers to elements in iframe
        const setupElementHandlers = () => {

            const allElements = iframeDocument.querySelectorAll('*')
            allElements.forEach(el => {
                // Check if element is an HTMLElement
                if (el instanceof (iframeWindow as any).HTMLElement) {
                    const htmlEl = el as HTMLElement
                    // Use the reusable setupElementEventHandlers function
                    setupElementEventHandlers(htmlEl)
                }
            })

            // Add mousemove event listener to the iframe document for hover detection
            iframeDocument.addEventListener('mousemove', handleIframeMouseMove as EventListener)

            // Add dragover, drop, and dragleave event listeners to the iframe document
            iframeDocument.addEventListener('dragover', handleDragOverWrapper as EventListener)
            iframeDocument.addEventListener('drop', handleDropWrapper as EventListener)
            iframeDocument.addEventListener('dragleave', handleDragLeaveWrapper as EventListener)
        }

        // Allow iframe content to render first
        setTimeout(setupElementHandlers, 100)

        // Update iframe height
        updateIframeHeight(iframe)

        // Cleanup event listeners when component unmounts or content changes
        return () => {
            removeEventHandlers(eventListenersRef.current)

            if (iframeDocument) {
                iframeDocument.removeEventListener('mousemove', handleIframeMouseMove as EventListener)
                iframeDocument.removeEventListener('dragover', handleDragOverWrapper as EventListener)
                iframeDocument.removeEventListener('drop', handleDropWrapper as EventListener)
                iframeDocument.removeEventListener('dragleave', handleDragLeaveWrapper as EventListener)
            }

        }
    }, [htmlContent, handleDragOverWrapper, handleDropWrapper, handleDragLeaveWrapper, setHtmlContent, setSelectedElement, setupElementEventHandlers])

    // Effect to draw margins on canvas when element is selected using the extracted function
    useEffect(() => {

        // If no element is selected, or refs aren't ready, just return early
        if (!selectedElement || !iframeRef.current || !canvasRef.current) {
            return
        }

        // Draw the overlay initially when the selected element changes
        handleDrawOverlay(selectedElement)

        // Setup overlay event listeners using the extracted function
        const cleanup = setupOverlayEventListeners(
            selectedElement,
            canvasRef.current,
            iframeRef.current
        )

        // Return cleanup function
        return cleanup
    }, [selectedElement, handleDrawOverlay])

    // Effect to draw hover overlay on canvas when element is hovered
    useEffect(() => {
        // Draw the hover overlay when the hovered element changes
        handleDrawHoverOverlay(hoveredElement)

        // If no element is hovered, or refs aren't ready, just return early
        if (!hoveredElement || !iframeRef.current || !hoverCanvasRef.current) {
            return
        }

        // Setup hover overlay event listeners
        const cleanup = setupHoverOverlayEventListeners(
            hoveredElement,
            hoverCanvasRef.current,
            iframeRef.current
        )

        // Return cleanup function
        return cleanup
    }, [hoveredElement, handleDrawHoverOverlay])

    // Handle keydown events for element deletion and copy-paste using the extracted function
    const handleKeyDownWrapper = useCallback((e: KeyboardEvent) => {
        handleKeyDown(
            e,
            selectedElement,
            iframeRef,
            setHtmlContent,
            setSelectedElement,
            handleStyleUpdate,
            setupElementEventHandlers
        )
    }, [handleStyleUpdate, selectedElement, setupElementEventHandlers])

    // Add keydown event listener
    useEffect(() => {
        const iframe = iframeRef.current
        if (!iframe || !iframe.contentDocument) return

        const iframeDocument = iframe.contentDocument
        iframeDocument.addEventListener('keydown', handleKeyDownWrapper)

        return () => {
            iframeDocument.removeEventListener('keydown', handleKeyDownWrapper)
        }
    }, [handleKeyDownWrapper, iframeRef, htmlContent])

    return (
        <div className="flex h-screen w-full">
            {!htmlContent ? (
                <div className="card m-auto">
                    <Button onClick={handleOpenProject}>
                        Open Project
                    </Button>
                </div>
            ) : (
                <>
                    {/* Left Panel - HTML Structure */}
                    <div className="w-[250px] h-full overflow-y-auto border-r border-border flex-shrink-0">
                        <StructurePanel
                            filename={filename}
                            htmlStructure={htmlStructure}
                            selectedElement={selectedElement}
                            onClearFile={handleClearSavedFile}
                            onElementSelect={(nodeId) => {
                                // Find the element in the iframe document based on the node structure
                                if (iframeRef.current && iframeRef.current.contentDocument && htmlStructure) {
                                    const iframe = iframeRef.current
                                    const iframeDocument = iframe.contentDocument

                                    // Find the node in the htmlStructure tree by ID
                                    const findNodeById = (node: HtmlNode | null, id: string): HtmlNode | null => {
                                        if (!node) return null
                                        if (node.id === id) return node

                                        for(const child of node.children) {
                                            const found = findNodeById(child, id)
                                            if (found) return found
                                        }

                                        return null
                                    }

                                    // Find the node in the tree
                                    const node = findNodeById(htmlStructure, nodeId)

                                    if (node) {
                                        // Get the path to this node (tag names and indices)
                                        const getNodePath = (targetNode: HtmlNode, currentNode: HtmlNode | null = htmlStructure, path: number[] = []): number[] | null => {
                                            if (!currentNode) return null
                                            if (currentNode.id === targetNode.id) return path

                                            for(let i = 0; i < currentNode.children.length; i++) {
                                                const childPath = getNodePath(targetNode, currentNode.children[i], [...path, i])
                                                if (childPath) return childPath
                                            }

                                            return null
                                        }

                                        const nodePath = getNodePath(node)

                                        if (nodePath && iframeDocument) {
                                            // Use the path to find the corresponding element in the iframe
                                            let element: Element = iframeDocument.body

                                            // Follow the path through the DOM
                                            for(const index of nodePath) {
                                                if (element.children[index]) {
                                                    element = element.children[index]
                                                } else {
                                                    console.error('Element not found at index', index)
                                                    return
                                                }
                                            }

                                            // Simulate a click on the element to select it
                                            const iframeWindow = iframe.contentWindow
                                            if (element instanceof (iframeWindow as any).HTMLElement) {
                                                // eslint-disable-next-line
                                                // @ts-ignore
                                                setSelectedElement(element)
                                            }
                                        }
                                    }
                                }
                            }}
                        />
                    </div>

                    {/* Center Panel - Rendered HTML using iframe */}
                    <div className="flex-1 h-full p-4 bg-muted/30 relative overflow-auto" style={{
                        backgroundSize: '20px 20px',
                        backgroundImage: 'linear-gradient(to right, hsl(var(--border)/30) 1px, transparent 1px), linear-gradient(to bottom, hsl(var(--border)/30) 1px, transparent 1px)'
                    }}>
                        <iframe
                            ref={iframeRef}
                            className="bg-background w-full h-full border-none overflow-auto"
                            title="HTML Preview"
                            sandbox="allow-same-origin allow-scripts"
                        />
                        {/* Canvas for selected element visualization */}
                        <canvas ref={canvasRef} style={{display: 'none'}}/>
                        {/* Canvas for hovered element visualization */}
                        <canvas ref={hoverCanvasRef} className="absolute pointer-events-none z-10 border-2 border-ring" style={{display: 'none'}}/>
                        {/* Drop indicator for drag and drop */}
                        <div
                            ref={dropIndicatorRef}
                            className="bg-primary shadow-sm"
                            style={{
                                display: 'none',
                                position: 'absolute',
                                transition: 'all 0.2s ease',
                                pointerEvents: 'none',
                                zIndex: 1000
                            }}
                        />
                    </div>

                    {/* Right Panel - Element Properties */}
                    <div className="w-[250px] h-full overflow-y-auto border-l border-border flex-shrink-0">
                        <PropertiesPanel
                            selectedElement={selectedElement}
                            onStyleChange={handleStyleUpdate}
                        />
                    </div>
                </>
            )}
        </div>
    )
}

export default App
